#!/usr/bin/env python3
"""
简单的HTTPS服务器，用于运行WebHID应用
WebHID API需要HTTPS环境才能正常工作
"""

import http.server
import ssl
import socketserver
import os
from pathlib import Path

# 服务器配置
PORT = 8443
DIRECTORY = "."

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def end_headers(self):
        # 添加CORS头部，允许跨域访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def create_self_signed_cert():
    """创建自签名证书"""
    try:
        from cryptography import x509
        from cryptography.x509.oid import NameOID
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization
        import datetime
        
        # 生成私钥
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # 创建证书
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Local"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Local"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "WebHID Test"),
            x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.datetime.utcnow()
        ).not_valid_after(
            datetime.datetime.utcnow() + datetime.timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.IPAddress("127.0.0.1"),
            ]),
            critical=False,
        ).sign(private_key, hashes.SHA256())
        
        # 保存证书和私钥
        with open("server.crt", "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        with open("server.key", "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        print("✅ 自签名证书创建成功")
        return True
        
    except ImportError:
        print("❌ 需要安装 cryptography 库来创建证书")
        print("请运行: pip install cryptography")
        return False
    except Exception as e:
        print(f"❌ 创建证书失败: {e}")
        return False

def main():
    print("🐺 狼蛛键盘Web驱动服务器")
    print("=" * 50)
    
    # 检查证书文件
    if not (Path("server.crt").exists() and Path("server.key").exists()):
        print("📜 证书文件不存在，正在创建自签名证书...")
        if not create_self_signed_cert():
            print("❌ 无法创建证书，服务器启动失败")
            return
    
    # 创建HTTP服务器
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        # 配置SSL
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain("server.crt", "server.key")
        httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
        
        print(f"🚀 HTTPS服务器启动成功！")
        print(f"📱 访问地址: https://localhost:{PORT}")
        print(f"📁 服务目录: {os.path.abspath(DIRECTORY)}")
        print(f"🔒 使用自签名证书 (浏览器会显示安全警告，点击继续即可)")
        print("\n💡 使用说明:")
        print("1. 在Chrome或Edge浏览器中访问上述地址")
        print("2. 忽略证书警告，点击'高级' -> '继续访问'")
        print("3. 点击'连接狼蛛键盘'按钮")
        print("4. 在弹出窗口中选择您的键盘设备")
        print("5. 开始控制RGB灯光效果！")
        print("\n按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n👋 服务器已停止")

if __name__ == "__main__":
    main()
