<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <title>狼蛛键盘驱动控制面板</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
          sans-serif;
        line-height: 1.6;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #333;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        text-align: center;
        color: white;
        margin-bottom: 30px;
      }

      .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .main-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
      }

      .panel {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
      }

      .panel h2 {
        color: #4a5568;
        margin-bottom: 20px;
        font-size: 1.4em;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 10px;
      }

      .connection-panel {
        text-align: center;
      }

      .device-status {
        display: inline-flex;
        align-items: center;
        gap: 10px;
        margin: 15px 0;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 500;
      }

      .status-disconnected {
        background-color: #fed7d7;
        color: #c53030;
      }

      .status-connected {
        background-color: #c6f6d5;
        color: #2f855a;
      }

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: currentColor;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      .btn {
        display: inline-block;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        border: none;
        border-radius: 8px;
        transition: all 0.3s ease;
        text-decoration: none;
        text-align: center;
      }

      .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .btn-primary:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }

      .btn:disabled {
        background-color: #e2e8f0;
        color: #a0aec0;
        cursor: not-allowed;
        transform: none;
      }

      .lighting-controls {
        display: none;
      }

      .lighting-controls.active {
        display: block;
      }

      .control-group {
        margin-bottom: 20px;
      }

      .control-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #4a5568;
      }

      .color-picker-group {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
      }

      .color-input {
        width: 60px;
        height: 40px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .slider-group {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .slider {
        flex: 1;
        height: 6px;
        border-radius: 3px;
        background: #e2e8f0;
        outline: none;
        -webkit-appearance: none;
      }

      .slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #667eea;
        cursor: pointer;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
      }

      .slider-value {
        min-width: 40px;
        text-align: center;
        font-weight: 500;
        color: #4a5568;
      }

      .preset-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
        margin-top: 15px;
      }

      .btn-preset {
        padding: 8px 16px;
        font-size: 14px;
        background: #f7fafc;
        color: #4a5568;
        border: 2px solid #e2e8f0;
      }

      .btn-preset:hover {
        background: #edf2f7;
        border-color: #cbd5e0;
      }

      .btn-preset.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
      }

      #log-container {
        grid-column: 1 / -1;
        margin-top: 20px;
        border: 1px solid #e2e8f0;
        border-radius: 10px;
        padding: 15px;
        height: 300px;
        overflow-y: auto;
        background-color: #f8fafc;
        font-family: 'Courier New', Courier, monospace;
        font-size: 13px;
      }

      .log-entry {
        padding: 4px 8px;
        border-radius: 4px;
        margin-bottom: 3px;
        border-left: 3px solid transparent;
      }

      .log-info {
        color: #3182ce;
        border-left-color: #3182ce;
        background-color: rgba(49, 130, 206, 0.1);
      }

      .log-success {
        color: #38a169;
        border-left-color: #38a169;
        background-color: rgba(56, 161, 105, 0.1);
      }

      .log-error {
        color: #e53e3e;
        border-left-color: #e53e3e;
        background-color: rgba(229, 62, 62, 0.1);
      }

      .log-data {
        color: #805ad5;
        border-left-color: #805ad5;
        background-color: rgba(128, 90, 213, 0.1);
      }

      @media (max-width: 768px) {
        .main-content {
          grid-template-columns: 1fr;
        }

        .color-picker-group,
        .slider-group {
          flex-direction: column;
          align-items: stretch;
        }

        .preset-buttons {
          grid-template-columns: 1fr 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🐺 狼蛛键盘驱动控制面板</h1>
        <p>专业的RGB灯光控制与键盘管理工具</p>
      </div>

      <div class="main-content">
        <!-- 连接面板 -->
        <div class="panel connection-panel">
          <h2>🔌 设备连接</h2>
          <div id="deviceStatus" class="device-status status-disconnected">
            <div class="status-indicator"></div>
            <span>设备未连接</span>
          </div>
          <div
            id="deviceInfo"
            style="display: none; margin: 15px 0; color: #666; font-size: 14px"
          ></div>
          <button id="connectButton" class="btn btn-primary">连接狼蛛键盘</button>
        </div>

        <!-- 灯光控制面板 -->
        <div class="panel">
          <h2>💡 RGB 灯光控制</h2>
          <div id="lightingControls" class="lighting-controls">
            <!-- 预设模式 -->
            <div class="control-group">
              <label>预设灯光模式</label>
              <div class="preset-buttons">
                <button class="btn btn-preset" data-mode="static">静态</button>
                <button class="btn btn-preset" data-mode="breathing">呼吸</button>
                <button class="btn btn-preset" data-mode="rainbow">彩虹</button>
                <button class="btn btn-preset" data-mode="wave">波浪</button>
                <button class="btn btn-preset" data-mode="reactive">反应</button>
                <button class="btn btn-preset" data-mode="off">关闭</button>
              </div>
            </div>

            <!-- 颜色选择 -->
            <div class="control-group">
              <label>自定义颜色</label>
              <div class="color-picker-group">
                <input type="color" id="colorPicker" class="color-input" value="#ff0000" />
                <button class="btn btn-preset" onclick="applyCustomColor()">应用颜色</button>
              </div>
            </div>

            <!-- 亮度控制 -->
            <div class="control-group">
              <label>亮度调节</label>
              <div class="slider-group">
                <input
                  type="range"
                  id="brightnessSlider"
                  class="slider"
                  min="0"
                  max="100"
                  value="100"
                />
                <span id="brightnessValue" class="slider-value">100%</span>
              </div>
            </div>

            <!-- 速度控制 -->
            <div class="control-group">
              <label>动画速度</label>
              <div class="slider-group">
                <input type="range" id="speedSlider" class="slider" min="1" max="10" value="5" />
                <span id="speedValue" class="slider-value">5</span>
              </div>
            </div>
          </div>

          <div id="lightingDisabled" style="text-align: center; color: #999; padding: 40px 0">
            <p>请先连接键盘以启用灯光控制</p>
          </div>
        </div>
      </div>

      <!-- 日志面板 -->
      <div class="panel">
        <h2>📋 实时日志</h2>
        <div id="log-container"></div>
      </div>
    </div>

    <script>
      // ==================================================================
      // ▼▼▼ 请在这里修改为你自己设备的 VID 和 PID ▼▼▼
      // vendorId 和 productId 都是十六进制数字，请以 0x 开头。
      // 你可以添加多个过滤器，或者留空以匹配所有设备。
      const myDeviceFilters = [
        // 这是你的设备信息，已经帮你填好了
        { vendorId: 0x3554, productId: 0xfa09 }

        // 其他设备示例 (可以删除或注释掉)
        // { vendorId: 0x045e, productId: 0x02ea }, // Xbox Controller
        // { vendorId: 0x057e, productId: 0x2009 }, // Switch Pro Controller
      ]
      // ▲▲▲ 请在这里修改为你自己设备的 VID 和 PID ▲▲▲
      // ==================================================================

      const connectButton = document.getElementById('connectButton')
      const logContainer = document.getElementById('log-container')
      let myDevice = null

      // 日志记录函数，带类型和颜色
      function log(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`)
        const entry = document.createElement('div')
        entry.className = `log-entry log-${type}`
        entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`
        logContainer.appendChild(entry)
        // 自动滚动到底部
        logContainer.scrollTop = logContainer.scrollHeight
      }

      // 检查 WebHID API 的可用性
      if (!('hid' in navigator)) {
        log('你的浏览器不支持 WebHID API，请使用最新版的 Chrome 或 Edge 浏览器。', 'error')
        connectButton.disabled = true
      }

      // UI 元素引用
      const deviceStatus = document.getElementById('deviceStatus')
      const deviceInfo = document.getElementById('deviceInfo')
      const lightingControls = document.getElementById('lightingControls')
      const lightingDisabled = document.getElementById('lightingDisabled')
      const brightnessSlider = document.getElementById('brightnessSlider')
      const brightnessValue = document.getElementById('brightnessValue')
      const speedSlider = document.getElementById('speedSlider')
      const speedValue = document.getElementById('speedValue')
      const colorPicker = document.getElementById('colorPicker')

      // 当前灯光设置
      let currentMode = 'static'
      let currentBrightness = 100
      let currentSpeed = 5
      let currentColor = { r: 255, g: 0, b: 0 }

      // 更新设备状态显示
      function updateDeviceStatus(connected, deviceName = '') {
        if (connected) {
          deviceStatus.className = 'device-status status-connected'
          deviceStatus.innerHTML = '<div class="status-indicator"></div><span>设备已连接</span>'
          deviceInfo.style.display = 'block'
          deviceInfo.textContent = deviceName
          lightingControls.classList.add('active')
          lightingDisabled.style.display = 'none'
          connectButton.textContent = '设备已连接'
          connectButton.disabled = true
        } else {
          deviceStatus.className = 'device-status status-disconnected'
          deviceStatus.innerHTML = '<div class="status-indicator"></div><span>设备未连接</span>'
          deviceInfo.style.display = 'none'
          lightingControls.classList.remove('active')
          lightingDisabled.style.display = 'block'
          connectButton.textContent = '连接狼蛛键盘'
          connectButton.disabled = false
        }
      }

      // 狼蛛键盘RGB控制命令
      const ReddragonCommands = {
        // 基础命令头
        HEADER: [0x04, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00],

        // 灯光模式命令
        MODES: {
          static: [
            0x04, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00
          ],
          breathing: [
            0x04, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00
          ],
          rainbow: [
            0x04, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00
          ],
          wave: [
            0x04, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00
          ],
          reactive: [
            0x04, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00
          ],
          off: [
            0x04, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00
          ]
        },

        // 创建颜色设置命令
        createColorCommand(r, g, b) {
          return [
            0x04,
            0x02,
            0x00,
            0x01,
            0x00,
            0x00,
            0x00,
            0x00,
            r,
            g,
            b,
            0x00,
            0x00,
            0x00,
            0x00,
            0x00
          ]
        },

        // 创建亮度设置命令
        createBrightnessCommand(brightness) {
          const level = Math.round((brightness / 100) * 255)
          return [
            0x04,
            0x03,
            0x00,
            0x01,
            0x00,
            0x00,
            0x00,
            0x00,
            level,
            0x00,
            0x00,
            0x00,
            0x00,
            0x00,
            0x00,
            0x00
          ]
        },

        // 创建速度设置命令
        createSpeedCommand(speed) {
          const level = Math.round((speed / 10) * 255)
          return [
            0x04,
            0x04,
            0x00,
            0x01,
            0x00,
            0x00,
            0x00,
            0x00,
            level,
            0x00,
            0x00,
            0x00,
            0x00,
            0x00,
            0x00,
            0x00
          ]
        }
      }

      // 发送HID命令到键盘
      async function sendCommand(command, description) {
        if (!myDevice || !myDevice.opened) {
          log('设备未连接，无法发送命令', 'error')
          return false
        }

        try {
          const data = new Uint8Array(command)
          await myDevice.sendReport(0x00, data)
          log(`${description} - 命令已发送: [${command.join(', ')}]`, 'success')
          return true
        } catch (error) {
          log(`发送命令失败: ${error.message}`, 'error')
          return false
        }
      }

      connectButton.addEventListener('click', async () => {
        log('正在请求设备，请在弹出的窗口中选择...', 'info')

        try {
          // 请求用户选择一个设备
          const devices = await navigator.hid.requestDevice({
            filters: myDeviceFilters
          })

          if (devices.length === 0) {
            log('没有选择任何设备。', 'info')
            return
          }

          myDevice = devices[0]
          const vid = myDevice.vendorId.toString(16).padStart(4, '0')
          const pid = myDevice.productId.toString(16).padStart(4, '0')
          const deviceName = `${myDevice.productName || '狼蛛键盘'} (VID: 0x${vid}, PID: 0x${pid})`

          log(`设备已选择: ${deviceName}`, 'success')

          // 打开设备连接
          await myDevice.open()
          log('设备已成功连接！正在初始化灯光控制...', 'success')

          // 更新UI状态
          updateDeviceStatus(true, deviceName)

          // 监听来自设备的数据
          myDevice.addEventListener('inputreport', (event) => {
            const { data, device, reportId } = event
            const dataBytes = new Uint8Array(data.buffer)
            const dataHex = Array.from(dataBytes)
              .map((b) => b.toString(16).padStart(2, '0'))
              .join(' ')
            log(
              `收到数据 (Report ID: ${reportId}): [${dataBytes.join(', ')}] (Hex: ${dataHex})`,
              'data'
            )
          })

          // 初始化默认灯光设置
          await sendCommand(ReddragonCommands.MODES.static, '设置静态模式')
          await sendCommand(ReddragonCommands.createColorCommand(255, 0, 0), '设置红色')
          await sendCommand(ReddragonCommands.createBrightnessCommand(100), '设置亮度100%')
        } catch (error) {
          log(`发生错误: ${error.message}`, 'error')
          console.error(error)
        }
      })

      // 预设模式按钮事件
      document.querySelectorAll('.btn-preset[data-mode]').forEach((button) => {
        button.addEventListener('click', async () => {
          const mode = button.dataset.mode
          currentMode = mode

          // 更新按钮状态
          document
            .querySelectorAll('.btn-preset[data-mode]')
            .forEach((btn) => btn.classList.remove('active'))
          button.classList.add('active')

          // 发送模式命令
          if (ReddragonCommands.MODES[mode]) {
            await sendCommand(ReddragonCommands.MODES[mode], `设置${button.textContent}模式`)
          }
        })
      })

      // 亮度滑块事件
      brightnessSlider.addEventListener('input', async (e) => {
        currentBrightness = parseInt(e.target.value)
        brightnessValue.textContent = `${currentBrightness}%`
        await sendCommand(
          ReddragonCommands.createBrightnessCommand(currentBrightness),
          `设置亮度${currentBrightness}%`
        )
      })

      // 速度滑块事件
      speedSlider.addEventListener('input', async (e) => {
        currentSpeed = parseInt(e.target.value)
        speedValue.textContent = currentSpeed
        await sendCommand(
          ReddragonCommands.createSpeedCommand(currentSpeed),
          `设置速度${currentSpeed}`
        )
      })

      // 颜色选择器事件
      colorPicker.addEventListener('change', (e) => {
        const hex = e.target.value
        currentColor = hexToRgb(hex)
      })

      // 应用自定义颜色函数
      window.applyCustomColor = async function () {
        const hex = colorPicker.value
        const rgb = hexToRgb(hex)
        currentColor = rgb
        await sendCommand(
          ReddragonCommands.createColorCommand(rgb.r, rgb.g, rgb.b),
          `设置颜色RGB(${rgb.r}, ${rgb.g}, ${rgb.b})`
        )
      }

      // 十六进制颜色转RGB
      function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
        return result
          ? {
              r: parseInt(result[1], 16),
              g: parseInt(result[2], 16),
              b: parseInt(result[3], 16)
            }
          : { r: 255, g: 0, b: 0 }
      }

      // 监听设备断开
      navigator.hid.addEventListener('disconnect', (event) => {
        if (event.device === myDevice) {
          log(`设备 ${myDevice.productName || '狼蛛键盘'} 已断开。`, 'error')
          myDevice = null
          updateDeviceStatus(false)
        }
      })

      // 页面关闭时，断开设备连接
      window.addEventListener('beforeunload', async () => {
        if (myDevice && myDevice.opened) {
          await myDevice.close()
        }
      })

      // 初始化UI状态
      updateDeviceStatus(false)
    </script>
  </body>
</html>
