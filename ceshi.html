<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>WebHID 调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #0056b3;
        }
        #connectButton {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        #connectButton:hover {
            background-color: #0056b3;
        }
        #connectButton:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #log-container {
            margin-top: 20px;
            border: 1px solid #ccc;
            padding: 10px;
            height: 400px;
            overflow-y: scroll;
            background-color: #fafafa;
            white-space: pre-wrap; /* 保持换行和空格 */
            font-family: "Courier New", Courier, monospace;
        }
        .log-entry {
            padding: 2px 5px;
            border-radius: 3px;
            margin-bottom: 2px;
        }
        .log-info { color: #0056b3; }
        .log-success { color: #155724; background-color: #d4edda; }
        .log-error { color: #721c24; background-color: #f8d7da; }
        .log-data { color: #555; }
    </style>
</head>
<body>

    <div class="container">
        <h1>WebHID 设备调试工具</h1>
        <p>
            <strong>使用说明:</strong>
            <ol>
                <li>修改本文件代码中的 `myDeviceFilters`，填入你设备的 VID 和 PID。</li>
                <li>将此 HTML 文件通过 HTTPS 服务器运行 (例如 VS Code 的 Live Server 插件)。</li>
                <li>在兼容的浏览器 (Chrome/Edge) 中打开页面。</li>
                <li>点击下面的按钮，然后在弹窗中选择你的设备。</li>
            </ol>
        </p>
        
        <button id="connectButton">连接 HID 设备</button>
        
        <h2>实时日志</h2>
        <div id="log-container"></div>
    </div>

    <script>
        // ==================================================================
        // ▼▼▼ 请在这里修改为你自己设备的 VID 和 PID ▼▼▼
        // vendorId 和 productId 都是十六进制数字，请以 0x 开头。
        // 你可以添加多个过滤器，或者留空以匹配所有设备。
        const myDeviceFilters = [
            // 这是你的设备信息，已经帮你填好了
            { vendorId: 0x3554, productId: 0xfa09 }, 
            
            // 其他设备示例 (可以删除或注释掉)
            // { vendorId: 0x045e, productId: 0x02ea }, // Xbox Controller
            // { vendorId: 0x057e, productId: 0x2009 }, // Switch Pro Controller
        ];
        // ▲▲▲ 请在这里修改为你自己设备的 VID 和 PID ▲▲▲
        // ==================================================================

        const connectButton = document.getElementById('connectButton');
        const logContainer = document.getElementById('log-container');
        let myDevice = null;

        // 日志记录函数，带类型和颜色
        function log(message, type = 'info') {
            console.log(`[${type.toUpperCase()}] ${message}`);
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            // 自动滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 检查 WebHID API 的可用性
        if (!("hid" in navigator)) {
            log("你的浏览器不支持 WebHID API，请使用最新版的 Chrome 或 Edge 浏览器。", 'error');
            connectButton.disabled = true;
        }

        connectButton.addEventListener('click', async () => {
            log("正在请求设备，请在弹出的窗口中选择...", 'info');

            try {
                // 请求用户选择一个设备
                const devices = await navigator.hid.requestDevice({
                    filters: myDeviceFilters
                });

                if (devices.length === 0) {
                    log("没有选择任何设备。", 'info');
                    return;
                }

                myDevice = devices[0];
                const vid = myDevice.vendorId.toString(16).padStart(4, '0');
                const pid = myDevice.productId.toString(16).padStart(4, '0');
                log(`设备已选择: ${myDevice.productName} (VID: 0x${vid}, PID: 0x${pid})`, 'success');

                // 打开设备连接
                await myDevice.open();
                console.log(myDevice)
                log("设备已成功连接！正在监听输入...", 'success');
                connectButton.textContent = '设备已连接';
                connectButton.disabled = true;

                // 监听来自设备的数据
                myDevice.addEventListener('inputreport', event => {
                    const { data, device, reportId } = event;
                    const dataBytes = new Uint8Array(data.buffer);
                    const dataHex = Array.from(dataBytes).map(b => b.toString(16).padStart(2, '0')).join(' ');
                    log(`收到数据 (Report ID: ${reportId}): [${dataBytes.join(', ')}] (Hex: ${dataHex})`, 'data');
                });

            } catch (error) {
                log(`发生错误: ${error.message}`, 'error');
                console.error(error);
            }
        });

        // 监听设备断开
        navigator.hid.addEventListener('disconnect', (event) => {
            if (event.device === myDevice) {
                log(`设备 ${myDevice.productName} 已断开。`, 'error');
                myDevice = null;
                connectButton.textContent = '连接 HID 设备';
                connectButton.disabled = false;
            }
        });

        // 页面关闭时，断开设备连接
        window.addEventListener('beforeunload', async () => {
            if (myDevice && myDevice.opened) {
                await myDevice.close();
            }
        });

    </script>
</body>
</html>
