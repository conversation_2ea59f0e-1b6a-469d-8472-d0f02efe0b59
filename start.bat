@echo off
echo 🐺 狼蛛键盘Web驱动启动器
echo ================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python 3.6+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
echo.

REM 尝试安装cryptography库
echo 📦 检查依赖库...
pip install cryptography >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 依赖库准备完成
) else (
    echo ⚠️  cryptography库安装失败，将使用简单HTTP服务器
)

echo.
echo 🚀 启动Web服务器...
echo.

REM 启动Python服务器
python server.py

pause
